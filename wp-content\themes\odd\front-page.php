<?php
/**
 * The front page template file
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <!-- Hero Section -->
        <section class="hero-section text-center mb-4">
            <h1 class="hero-title">Find the Best Dentists in Odessa</h1>
            <p class="hero-description">Your comprehensive directory for quality dental care. Discover top-rated dentists, orthodontists, and dental specialists in your area.</p>
            
            <!-- Featured Search -->
            <div class="hero-search">
                <form method="get" action="<?php echo esc_url(home_url('/')); ?>" class="hero-search-form">
                    <div class="search-row">
                        <input type="search" 
                               name="s" 
                               placeholder="Search for dentists, services, or locations..." 
                               class="hero-search-input">
                        <button type="submit" class="hero-search-button">Find Dentists</button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Directory Categories -->
        <section class="directory-categories mb-4">
            <h2 class="section-title text-center">Browse by Dental Services</h2>
            
            <?php
            $directories = get_terms(array(
                'taxonomy' => 'directory',
                'hide_empty' => false,
                'number' => 8
            ));
            
            if (!empty($directories) && !is_wp_error($directories)) :
            ?>
                <div class="categories-grid">
                    <?php foreach ($directories as $directory) : ?>
                        <div class="category-card">
                            <a href="<?php echo esc_url(get_term_link($directory)); ?>" class="category-link">
                                <h3 class="category-title"><?php echo esc_html($directory->name); ?></h3>
                                <p class="category-count"><?php echo $directory->count; ?> listings</p>
                                <?php if ($directory->description) : ?>
                                    <p class="category-description"><?php echo esc_html($directory->description); ?></p>
                                <?php endif; ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </section>

        <!-- Featured Listings -->
        <section class="featured-listings mb-4">
            <h2 class="section-title text-center">Featured Dental Practices</h2>
            
            <?php
            // Get featured listings (top-rated or recently added)
            $featured_query = new WP_Query(array(
                'post_type' => 'listing',
                'posts_per_page' => 6,
                'meta_query' => array(
                    array(
                        'key' => 'listing_rating',
                        'value' => '4',
                        'compare' => '>='
                    )
                ),
                'orderby' => 'meta_value_num',
                'meta_key' => 'listing_rating',
                'order' => 'DESC'
            ));
            
            if ($featured_query->have_posts()) :
            ?>
                <div class="directory-grid">
                    <?php
                    while ($featured_query->have_posts()) :
                        $featured_query->the_post();
                        
                        // Get listing data (updated field names)
                        $listing_name = get_the_title(); // Use post title instead of listing_name
                        $listing_phone = get_post_meta(get_the_ID(), 'listing_phone', true);
                        $listing_address = get_post_meta(get_the_ID(), 'listing_full_address', true);
                        $listing_city = get_post_meta(get_the_ID(), 'listing_city', true);
                        $listing_rating = get_post_meta(get_the_ID(), 'listing_rating', true);
                        $listing_reviews = get_post_meta(get_the_ID(), 'listing_reviews', true);
                        $listing_photo = get_post_meta(get_the_ID(), 'listing_photo', true);
                        
                        // Get directory terms
                        $directories = get_the_terms(get_the_ID(), 'directory');
                    ?>
                        <article class="listing-card">
                            <?php if ($listing_photo) : ?>
                                <div class="listing-image">
                                    <img src="<?php echo esc_url($listing_photo); ?>" alt="<?php echo esc_attr($listing_name); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <div class="listing-content">
                                <h3 class="listing-title">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php echo esc_html($listing_name); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($directories && !is_wp_error($directories)) : ?>
                                    <div class="listing-meta">
                                        <?php
                                        $directory_names = array();
                                        foreach ($directories as $directory) {
                                            $directory_names[] = $directory->name;
                                        }
                                        echo esc_html(implode(', ', $directory_names));
                                        ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_address) : ?>
                                    <div class="listing-address">
                                        📍 <?php echo esc_html($listing_address); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_phone) : ?>
                                    <div class="listing-phone">
                                        📞 <a href="tel:<?php echo esc_attr($listing_phone); ?>">
                                            <?php echo esc_html($listing_phone); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_rating) : ?>
                                    <div class="listing-rating">
                                        <span class="stars"><?php echo get_rating_stars($listing_rating); ?></span>
                                        <span class="rating-text">
                                            <?php echo esc_html($listing_rating); ?>
                                            <?php if ($listing_reviews) : ?>
                                                (<?php echo esc_html($listing_reviews); ?> reviews)
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="listing-actions">
                                    <a href="<?php the_permalink(); ?>" class="btn">View Details</a>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
                
                <div class="text-center mt-3">
                    <a href="<?php echo esc_url(get_post_type_archive_link('listing')); ?>" class="btn btn-secondary">
                        View All Dentists
                    </a>
                </div>
                
            <?php
                wp_reset_postdata();
            endif;
            ?>
        </section>

        <!-- Call to Action -->
        <section class="cta-section text-center mb-4">
            <div class="cta-content">
                <h2>Are you a dental professional?</h2>
                <p>Join our directory and connect with patients in Odessa looking for quality dental care.</p>
                <a href="<?php echo esc_url(home_url('/add-listing')); ?>" class="btn">Add Your Practice</a>
            </div>
        </section>

    </div><!-- .container -->
</main><!-- #main -->

<?php
get_footer();
