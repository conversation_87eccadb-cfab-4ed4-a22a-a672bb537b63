<?php
/**
 * The template for displaying search results pages
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <header class="page-header">
            <h1 class="page-title">
                <?php
                printf(
                    esc_html__('Search Results for: %s', 'odd'),
                    '<span>' . get_search_query() . '</span>'
                );
                ?>
            </h1>
            
            <?php if (have_posts()) : ?>
                <div class="search-meta">
                    <p>Found <?php echo $wp_query->found_posts; ?> results</p>
                </div>
            <?php endif; ?>
        </header><!-- .page-header -->

        <!-- Search Form -->
        <div class="search-filters">
            <form method="get" action="<?php echo esc_url(home_url('/')); ?>" class="directory-search-form">
                <div class="filter-row">
                    <input type="search" 
                           name="s" 
                           value="<?php echo get_search_query(); ?>" 
                           placeholder="Search dentists..." 
                           class="search-input">
                    
                    <select name="listing_city" class="filter-select">
                        <option value="">All Cities</option>
                        <?php
                        // Get unique cities from listings
                        global $wpdb;
                        $cities = $wpdb->get_col("
                            SELECT DISTINCT meta_value 
                            FROM {$wpdb->postmeta} 
                            WHERE meta_key = 'listing_city' 
                            AND meta_value != '' 
                            ORDER BY meta_value ASC
                        ");
                        
                        $selected_city = get_query_var('listing_city');
                        foreach ($cities as $city) :
                            $selected = ($selected_city == $city) ? 'selected' : '';
                        ?>
                            <option value="<?php echo esc_attr($city); ?>" <?php echo $selected; ?>>
                                <?php echo esc_html($city); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <select name="directory_filter" class="filter-select">
                        <option value="">All Services</option>
                        <?php
                        $directories = get_terms(array(
                            'taxonomy' => 'directory',
                            'hide_empty' => false
                        ));
                        
                        $selected_directory = get_query_var('directory_filter');
                        if (!empty($directories) && !is_wp_error($directories)) :
                            foreach ($directories as $directory) :
                                $selected = ($selected_directory == $directory->slug) ? 'selected' : '';
                            ?>
                                <option value="<?php echo esc_attr($directory->slug); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($directory->name); ?>
                                </option>
                            <?php endforeach;
                        endif; ?>
                    </select>
                    
                    <button type="submit" class="search-button">Search</button>
                </div>
            </form>
        </div>

        <?php if (have_posts()) : ?>

            <div class="directory-grid">
                <?php
                while (have_posts()) :
                    the_post();
                    
                    if (get_post_type() === 'listing') :
                        // Get listing data (updated field names)
                        $listing_name = get_the_title(); // Use post title instead of listing_name
                        $listing_phone = get_post_meta(get_the_ID(), 'listing_phone', true);
                        $listing_address = get_post_meta(get_the_ID(), 'listing_full_address', true);
                        $listing_city = get_post_meta(get_the_ID(), 'listing_city', true);
                        $listing_rating = get_post_meta(get_the_ID(), 'listing_rating', true);
                        $listing_reviews = get_post_meta(get_the_ID(), 'listing_reviews', true);
                        $listing_photo = get_post_meta(get_the_ID(), 'listing_photo', true);
                        $listing_about = get_post_meta(get_the_ID(), 'listing_about', true);
                        
                        // Get directory terms
                        $directories = get_the_terms(get_the_ID(), 'directory');
                    ?>
                        <article class="listing-card">
                            <?php if ($listing_photo) : ?>
                                <div class="listing-image">
                                    <img src="<?php echo esc_url($listing_photo); ?>" alt="<?php echo esc_attr($listing_name); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <div class="listing-content">
                                <h3 class="listing-title">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php echo esc_html($listing_name); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($directories && !is_wp_error($directories)) : ?>
                                    <div class="listing-meta">
                                        <?php
                                        $directory_names = array();
                                        foreach ($directories as $directory) {
                                            $directory_names[] = $directory->name;
                                        }
                                        echo esc_html(implode(', ', $directory_names));
                                        ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_about) : ?>
                                    <div class="listing-excerpt">
                                        <?php echo wp_trim_words(wp_strip_all_tags($listing_about), 20, '...'); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_address) : ?>
                                    <div class="listing-address">
                                        📍 <?php echo esc_html($listing_address); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_phone) : ?>
                                    <div class="listing-phone">
                                        📞 <a href="tel:<?php echo esc_attr($listing_phone); ?>">
                                            <?php echo esc_html($listing_phone); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($listing_rating) : ?>
                                    <div class="listing-rating">
                                        <span class="stars"><?php echo get_rating_stars($listing_rating); ?></span>
                                        <span class="rating-text">
                                            <?php echo esc_html($listing_rating); ?>
                                            <?php if ($listing_reviews) : ?>
                                                (<?php echo esc_html($listing_reviews); ?> reviews)
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="listing-actions">
                                    <a href="<?php the_permalink(); ?>" class="btn">View Details</a>
                                </div>
                            </div>
                        </article>
                    
                    <?php else : ?>
                        <!-- Regular post/page search result -->
                        <article id="post-<?php the_ID(); ?>" <?php post_class('search-result'); ?>>
                            <header class="entry-header">
                                <h3 class="entry-title">
                                    <a href="<?php the_permalink(); ?>" rel="bookmark">
                                        <?php the_title(); ?>
                                    </a>
                                </h3>
                                
                                <div class="entry-meta">
                                    <span class="post-type"><?php echo get_post_type_object(get_post_type())->labels->singular_name; ?></span>
                                </div>
                            </header><!-- .entry-header -->

                            <div class="entry-summary">
                                <?php the_excerpt(); ?>
                            </div><!-- .entry-summary -->
                        </article><!-- #post-<?php the_ID(); ?> -->
                    
                    <?php endif; ?>
                    
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size'  => 2,
                'prev_text' => __('Previous', 'odd'),
                'next_text' => __('Next', 'odd'),
            ));
            ?>

        <?php else : ?>

            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title"><?php esc_html_e('Nothing found', 'odd'); ?></h1>
                </header><!-- .page-header -->

                <div class="page-content">
                    <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'odd'); ?></p>
                    
                    <h3>Search suggestions:</h3>
                    <ul>
                        <li>Try different keywords</li>
                        <li>Try more general keywords</li>
                        <li>Check your spelling</li>
                        <li>Browse our <a href="<?php echo esc_url(get_post_type_archive_link('listing')); ?>">complete directory</a></li>
                    </ul>
                    
                    <?php
                    // Show popular directory terms
                    $popular_directories = get_terms(array(
                        'taxonomy' => 'directory',
                        'hide_empty' => true,
                        'number' => 6,
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ));
                    
                    if (!empty($popular_directories) && !is_wp_error($popular_directories)) :
                    ?>
                        <h3>Popular dental services:</h3>
                        <div class="popular-categories">
                            <?php foreach ($popular_directories as $directory) : ?>
                                <a href="<?php echo esc_url(get_term_link($directory)); ?>" class="btn btn-secondary">
                                    <?php echo esc_html($directory->name); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div><!-- .page-content -->
            </section><!-- .no-results -->

        <?php endif; ?>
        
    </div><!-- .container -->
</main><!-- #main -->

<?php
get_footer();
