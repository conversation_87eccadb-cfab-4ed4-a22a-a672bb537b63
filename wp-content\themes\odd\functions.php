<?php
/**
 * Odessa Dentists Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function odd_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'odd'),
    ));
}
add_action('after_setup_theme', 'odd_theme_setup');

/**
 * Enqueue scripts and styles
 */
function odd_scripts() {
    wp_enqueue_style('odd-style', get_stylesheet_uri());
    wp_enqueue_script('odd-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'odd_scripts');

/**
 * Register Custom Post Type: Listing
 */
function register_listing_post_type() {
    $labels = array(
        'name'                  => _x('Listings', 'Post Type General Name', 'odd'),
        'singular_name'         => _x('Listing', 'Post Type Singular Name', 'odd'),
        'menu_name'             => __('Dental Listings', 'odd'),
        'name_admin_bar'        => __('Listing', 'odd'),
        'archives'              => __('Listing Archives', 'odd'),
        'attributes'            => __('Listing Attributes', 'odd'),
        'parent_item_colon'     => __('Parent Listing:', 'odd'),
        'all_items'             => __('All Listings', 'odd'),
        'add_new_item'          => __('Add New Listing', 'odd'),
        'add_new'               => __('Add New', 'odd'),
        'new_item'              => __('New Listing', 'odd'),
        'edit_item'             => __('Edit Listing', 'odd'),
        'update_item'           => __('Update Listing', 'odd'),
        'view_item'             => __('View Listing', 'odd'),
        'view_items'            => __('View Listings', 'odd'),
        'search_items'          => __('Search Listing', 'odd'),
        'not_found'             => __('Not found', 'odd'),
        'not_found_in_trash'    => __('Not found in Trash', 'odd'),
        'featured_image'        => __('Featured Image', 'odd'),
        'set_featured_image'    => __('Set featured image', 'odd'),
        'remove_featured_image' => __('Remove featured image', 'odd'),
        'use_featured_image'    => __('Use as featured image', 'odd'),
        'insert_into_item'      => __('Insert into listing', 'odd'),
        'uploaded_to_this_item' => __('Uploaded to this listing', 'odd'),
        'items_list'            => __('Listings list', 'odd'),
        'items_list_navigation' => __('Listings list navigation', 'odd'),
        'filter_items_list'     => __('Filter listings list', 'odd'),
    );
    
    $args = array(
        'label'                 => __('Listing', 'odd'),
        'description'           => __('Dental practice listings', 'odd'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('directory'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-location-alt',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
    );
    
    register_post_type('listing', $args);
}
add_action('init', 'register_listing_post_type', 0);

/**
 * Register Custom Taxonomy: Directory
 */
function register_directory_taxonomy() {
    $labels = array(
        'name'                       => _x('Directories', 'Taxonomy General Name', 'odd'),
        'singular_name'              => _x('Directory', 'Taxonomy Singular Name', 'odd'),
        'menu_name'                  => __('Directories', 'odd'),
        'all_items'                  => __('All Directories', 'odd'),
        'parent_item'                => __('Parent Directory', 'odd'),
        'parent_item_colon'          => __('Parent Directory:', 'odd'),
        'new_item_name'              => __('New Directory Name', 'odd'),
        'add_new_item'               => __('Add New Directory', 'odd'),
        'edit_item'                  => __('Edit Directory', 'odd'),
        'update_item'                => __('Update Directory', 'odd'),
        'view_item'                  => __('View Directory', 'odd'),
        'separate_items_with_commas' => __('Separate directories with commas', 'odd'),
        'add_or_remove_items'        => __('Add or remove directories', 'odd'),
        'choose_from_most_used'      => __('Choose from the most used', 'odd'),
        'popular_items'              => __('Popular Directories', 'odd'),
        'search_items'               => __('Search Directories', 'odd'),
        'not_found'                  => __('Not Found', 'odd'),
        'no_terms'                   => __('No directories', 'odd'),
        'items_list'                 => __('Directories list', 'odd'),
        'items_list_navigation'      => __('Directories list navigation', 'odd'),
    );
    
    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
    );
    
    register_taxonomy('directory', array('listing'), $args);
}
add_action('init', 'register_directory_taxonomy', 0);

/**
 * Register Custom Fields for Listings
 */
function register_listing_custom_fields() {
    // Basic Info Fields (removed: query, name, name_for_emails)
    $basic_fields = array(
        'listing_website', 'listing_subtypes', 'listing_category', 'listing_type'
    );

    // Contact Fields
    $contact_fields = array(
        'listing_phone', 'listing_email_1', 'listing_email_1_full_name',
        'listing_email_1_first_name', 'listing_email_1_last_name',
        'listing_email_1_title', 'listing_email_1_phone'
    );

    // Location Fields (removed: us_state, country_code, time_zone, plus_code; corrected: zip, lat, long)
    $location_fields = array(
        'listing_full_address', 'listing_borough', 'listing_street', 'listing_city',
        'listing_zip', 'listing_state', 'listing_country',
        'listing_lat', 'listing_long', 'listing_h3', 'listing_area_service'
    );

    // Business Data Fields (removed: photos_count, range)
    $business_fields = array(
        'listing_rating', 'listing_reviews', 'listing_reviews_link',
        'listing_photo', 'listing_street_view', 'listing_about',
        'listing_logo', 'listing_verified'
    );

    // Links Fields
    $link_fields = array(
        'listing_booking_appointment_link', 'listing_location_link', 'listing_location_reviews_link'
    );

    // ID Fields
    $id_fields = array(
        'listing_place_id', 'listing_google_id', 'listing_cid', 'listing_kgmid', 'listing_reviews_id'
    );

    // Social Media Fields
    $social_fields = array(
        'listing_facebook', 'listing_instagram', 'listing_linkedin',
        'listing_tiktok', 'listing_twitter', 'listing_youtube'
    );

    // Hours Fields
    $hours_fields = array(
        'listing_working_hours', 'listing_popular_times'
    );

    // Combine all fields
    $all_fields = array_merge(
        $basic_fields, $contact_fields, $location_fields,
        $business_fields, $link_fields, $id_fields,
        $social_fields, $hours_fields
    );

    // Register each field as a meta field
    foreach ($all_fields as $field) {
        register_meta('post', $field, array(
            'type' => 'string',
            'description' => 'Listing custom field: ' . $field,
            'single' => true,
            'show_in_rest' => true,
        ));
    }
}
add_action('init', 'register_listing_custom_fields');

/**
 * Add Custom Fields Meta Box
 */
function add_listing_meta_boxes() {
    add_meta_box(
        'listing_basic_info',
        'Basic Information',
        'listing_basic_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_contact_info',
        'Contact Information',
        'listing_contact_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_location_info',
        'Location Information',
        'listing_location_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_business_info',
        'Business Information',
        'listing_business_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_social_info',
        'Social Media & Links',
        'listing_social_info_callback',
        'listing',
        'normal',
        'high'
    );
    
    add_meta_box(
        'listing_hours_info',
        'Working Hours',
        'listing_hours_info_callback',
        'listing',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_listing_meta_boxes');

/**
 * Basic Info Meta Box Callback
 */
function listing_basic_info_callback($post) {
    wp_nonce_field('listing_meta_box', 'listing_meta_box_nonce');

    $fields = array(
        'listing_website' => 'Website',
        'listing_subtypes' => 'Subtypes',
        'listing_category' => 'Category',
        'listing_type' => 'Type'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        if ($field === 'listing_website') {
            echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="https://example.com" /></p>';
        } else {
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Contact Info Meta Box Callback
 */
function listing_contact_info_callback($post) {
    $fields = array(
        'listing_phone' => 'Phone',
        'listing_email_1' => 'Email',
        'listing_email_1_full_name' => 'Contact Full Name',
        'listing_email_1_first_name' => 'Contact First Name',
        'listing_email_1_last_name' => 'Contact Last Name',
        'listing_email_1_title' => 'Contact Title',
        'listing_email_1_phone' => 'Contact Phone'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
    }
}

/**
 * Location Info Meta Box Callback
 */
function listing_location_info_callback($post) {
    $fields = array(
        'listing_full_address' => 'Full Address',
        'listing_borough' => 'Borough',
        'listing_street' => 'Street',
        'listing_city' => 'City',
        'listing_zip' => 'ZIP Code',
        'listing_state' => 'State',
        'listing_country' => 'Country',
        'listing_lat' => 'Latitude',
        'listing_long' => 'Longitude',
        'listing_h3' => 'H3',
        'listing_area_service' => 'Area Service'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        if (in_array($field, ['listing_lat', 'listing_long'])) {
            echo '<input type="number" step="any" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="' . ($field === 'listing_lat' ? 'e.g., 46.4774' : 'e.g., -30.7226') . '" /></p>';
        } else {
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Business Info Meta Box Callback
 */
function listing_business_info_callback($post) {
    $fields = array(
        'listing_rating' => 'Rating',
        'listing_reviews' => 'Reviews Count',
        'listing_reviews_link' => 'Reviews Link',
        'listing_photo' => 'Photo URL',
        'listing_street_view' => 'Street View URL',
        'listing_about' => 'About',
        'listing_logo' => 'Logo URL',
        'listing_verified' => 'Verified',
        'listing_booking_appointment_link' => 'Booking Link',
        'listing_location_link' => 'Location Link',
        'listing_location_reviews_link' => 'Location Reviews Link',
        'listing_place_id' => 'Place ID',
        'listing_google_id' => 'Google ID',
        'listing_cid' => 'CID',
        'listing_kgmid' => 'KGMID',
        'listing_reviews_id' => 'Reviews ID'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        if ($field === 'listing_about') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<textarea id="' . $field . '" name="' . $field . '" style="width: 100%; height: 100px;">' . esc_textarea($value) . '</textarea></p>';
        } elseif ($field === 'listing_rating') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="number" step="0.1" min="0" max="5" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" placeholder="0.0 - 5.0" /></p>';
        } elseif ($field === 'listing_reviews') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="number" min="0" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        } elseif (in_array($field, ['listing_photo', 'listing_logo', 'listing_reviews_link', 'listing_booking_appointment_link', 'listing_location_link', 'listing_location_reviews_link', 'listing_street_view'])) {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        } elseif ($field === 'listing_verified') {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<select id="' . $field . '" name="' . $field . '" style="width: 100%;">';
            echo '<option value="">Not Verified</option>';
            echo '<option value="1"' . selected($value, '1', false) . '>Verified</option>';
            echo '</select></p>';
        } else {
            echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
            echo '<input type="text" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
        }
    }
}

/**
 * Social Media Meta Box Callback
 */
function listing_social_info_callback($post) {
    $fields = array(
        'listing_facebook' => 'Facebook',
        'listing_instagram' => 'Instagram',
        'listing_linkedin' => 'LinkedIn',
        'listing_tiktok' => 'TikTok',
        'listing_twitter' => 'Twitter',
        'listing_youtube' => 'YouTube'
    );

    foreach ($fields as $field => $label) {
        $value = get_post_meta($post->ID, $field, true);
        echo '<p><label for="' . $field . '">' . $label . ':</label><br>';
        echo '<input type="url" id="' . $field . '" name="' . $field . '" value="' . esc_attr($value) . '" style="width: 100%;" /></p>';
    }
}

/**
 * Working Hours Meta Box Callback
 */
function listing_hours_info_callback($post) {
    $working_hours = get_post_meta($post->ID, 'listing_working_hours', true);
    $popular_times = get_post_meta($post->ID, 'listing_popular_times', true);

    echo '<p><label for="listing_working_hours">Working Hours (JSON format):</label><br>';
    echo '<textarea id="listing_working_hours" name="listing_working_hours" style="width: 100%; height: 200px;">' . esc_textarea($working_hours) . '</textarea></p>';
    echo '<p><small>Format: {"work_hours": {"timetable": {"monday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}], ...}, "current_status": "open"}}</small></p>';

    echo '<p><label for="listing_popular_times">Popular Times (JSON format):</label><br>';
    echo '<textarea id="listing_popular_times" name="listing_popular_times" style="width: 100%; height: 100px;">' . esc_textarea($popular_times) . '</textarea></p>';
}

/**
 * Save Custom Fields
 */
function save_listing_meta_box_data($post_id) {
    if (!isset($_POST['listing_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['listing_meta_box_nonce'], 'listing_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && 'listing' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    // List of all custom fields (updated to match corrected field names)
    $fields = array(
        'listing_website', 'listing_subtypes', 'listing_category', 'listing_type', 'listing_phone',
        'listing_email_1', 'listing_email_1_full_name', 'listing_email_1_first_name',
        'listing_email_1_last_name', 'listing_email_1_title', 'listing_email_1_phone',
        'listing_full_address', 'listing_borough', 'listing_street', 'listing_city',
        'listing_zip', 'listing_state', 'listing_country',
        'listing_lat', 'listing_long', 'listing_h3', 'listing_area_service', 'listing_rating',
        'listing_reviews', 'listing_reviews_link', 'listing_photo',
        'listing_street_view', 'listing_about', 'listing_logo',
        'listing_verified', 'listing_booking_appointment_link', 'listing_location_link',
        'listing_location_reviews_link', 'listing_place_id', 'listing_google_id',
        'listing_cid', 'listing_kgmid', 'listing_reviews_id', 'listing_facebook',
        'listing_instagram', 'listing_linkedin', 'listing_tiktok', 'listing_twitter',
        'listing_youtube', 'listing_working_hours', 'listing_popular_times'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_listing_meta_box_data');

/**
 * Helper function to format working hours
 */
function format_working_hours($working_hours_json) {
    if (empty($working_hours_json)) {
        return array();
    }

    $hours_data = json_decode($working_hours_json, true);
    if (!$hours_data || !isset($hours_data['work_hours']['timetable'])) {
        return array();
    }

    $timetable = $hours_data['work_hours']['timetable'];
    $formatted_hours = array();

    $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');

    foreach ($days as $day) {
        if (isset($timetable[$day]) && !empty($timetable[$day])) {
            $day_hours = $timetable[$day][0];
            if ($day_hours['open']['hour'] == 0 && $day_hours['open']['minute'] == 0 &&
                $day_hours['close']['hour'] == 0 && $day_hours['close']['minute'] == 0) {
                $formatted_hours[$day] = 'Closed';
            } else {
                $open_time = sprintf('%02d:%02d', $day_hours['open']['hour'], $day_hours['open']['minute']);
                $close_time = sprintf('%02d:%02d', $day_hours['close']['hour'], $day_hours['close']['minute']);
                $formatted_hours[$day] = $open_time . ' - ' . $close_time;
            }
        } else {
            $formatted_hours[$day] = 'Closed';
        }
    }

    return $formatted_hours;
}

/**
 * Helper function to get listing rating stars
 */
function get_rating_stars($rating) {
    if (empty($rating)) {
        return '';
    }

    $rating = floatval($rating);
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5 ? 1 : 0;
    $empty_stars = 5 - $full_stars - $half_star;

    $stars = str_repeat('★', $full_stars);
    if ($half_star) {
        $stars .= '☆';
    }
    $stars .= str_repeat('☆', $empty_stars);

    return $stars;
}

/**
 * Helper function to get social media links
 */
function get_social_media_links($post_id) {
    $social_fields = array(
        'listing_facebook' => 'Facebook',
        'listing_instagram' => 'Instagram',
        'listing_linkedin' => 'LinkedIn',
        'listing_tiktok' => 'TikTok',
        'listing_twitter' => 'Twitter',
        'listing_youtube' => 'YouTube'
    );

    $social_links = array();

    foreach ($social_fields as $field => $label) {
        $url = get_post_meta($post_id, $field, true);
        if (!empty($url)) {
            $social_links[$label] = $url;
        }
    }

    return $social_links;
}

/**
 * Custom search function for listings
 */
function custom_listing_search($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_search()) {
            $query->set('post_type', array('listing'));
        }
    }
}
add_action('pre_get_posts', 'custom_listing_search');

/**
 * Add custom query vars for filtering
 */
function add_custom_query_vars($vars) {
    $vars[] = 'listing_city';
    $vars[] = 'listing_rating';
    $vars[] = 'directory_filter';
    return $vars;
}
add_filter('query_vars', 'add_custom_query_vars');

/**
 * Modify main query for filtering
 */
function modify_main_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_home() || is_front_page() || is_post_type_archive('listing')) {
            $query->set('post_type', 'listing');
            $query->set('posts_per_page', 12);

            // Handle city filter
            $city_filter = get_query_var('listing_city');
            if (!empty($city_filter)) {
                $meta_query = $query->get('meta_query') ?: array();
                $meta_query[] = array(
                    'key' => 'listing_city',
                    'value' => $city_filter,
                    'compare' => 'LIKE'
                );
                $query->set('meta_query', $meta_query);
            }

            // Handle rating filter
            $rating_filter = get_query_var('listing_rating');
            if (!empty($rating_filter)) {
                $meta_query = $query->get('meta_query') ?: array();
                $meta_query[] = array(
                    'key' => 'listing_rating',
                    'value' => $rating_filter,
                    'compare' => '>='
                );
                $query->set('meta_query', $meta_query);
            }

            // Handle directory taxonomy filter
            $directory_filter = get_query_var('directory_filter');
            if (!empty($directory_filter)) {
                $query->set('directory', $directory_filter);
            }
        }
    }
}
add_action('pre_get_posts', 'modify_main_query');
