<?php
/**
 * The template for displaying listing archives
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php if (have_posts()) : ?>
            
            <header class="page-header">
                <h1 class="page-title">All Dental Practices in Odessa</h1>
                <p class="page-description">Browse our complete directory of dental care providers</p>
                
                <div class="archive-meta">
                    <p>Showing <?php echo $wp_query->found_posts; ?> dental practices</p>
                </div>
            </header><!-- .page-header -->

            <!-- Search and Filter Section -->
            <div class="search-filters">
                <form method="get" action="<?php echo esc_url(get_post_type_archive_link('listing')); ?>" class="directory-search-form">
                    <div class="filter-row">
                        <input type="search" 
                               name="s" 
                               value="<?php echo get_search_query(); ?>" 
                               placeholder="Search dentists..." 
                               class="search-input">
                        
                        <select name="listing_city" class="filter-select">
                            <option value="">All Cities</option>
                            <?php
                            // Get unique cities from listings
                            global $wpdb;
                            $cities = $wpdb->get_col("
                                SELECT DISTINCT meta_value 
                                FROM {$wpdb->postmeta} 
                                WHERE meta_key = 'listing_city' 
                                AND meta_value != '' 
                                ORDER BY meta_value ASC
                            ");
                            
                            $selected_city = get_query_var('listing_city');
                            foreach ($cities as $city) :
                                $selected = ($selected_city == $city) ? 'selected' : '';
                            ?>
                                <option value="<?php echo esc_attr($city); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($city); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="listing_rating" class="filter-select">
                            <option value="">All Ratings</option>
                            <?php
                            $selected_rating = get_query_var('listing_rating');
                            for ($i = 5; $i >= 1; $i--) :
                                $selected = ($selected_rating == $i) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $i; ?>" <?php echo $selected; ?>>
                                    <?php echo $i; ?>+ Stars
                                </option>
                            <?php endfor; ?>
                        </select>
                        
                        <select name="directory_filter" class="filter-select">
                            <option value="">All Services</option>
                            <?php
                            $directories = get_terms(array(
                                'taxonomy' => 'directory',
                                'hide_empty' => false
                            ));
                            
                            $selected_directory = get_query_var('directory_filter');
                            if (!empty($directories) && !is_wp_error($directories)) :
                                foreach ($directories as $directory) :
                                    $selected = ($selected_directory == $directory->slug) ? 'selected' : '';
                                ?>
                                    <option value="<?php echo esc_attr($directory->slug); ?>" <?php echo $selected; ?>>
                                        <?php echo esc_html($directory->name); ?>
                                    </option>
                                <?php endforeach;
                            endif; ?>
                        </select>
                        
                        <select name="orderby" class="filter-select">
                            <option value="">Sort By</option>
                            <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>Name A-Z</option>
                            <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>>Highest Rated</option>
                            <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>Recently Added</option>
                        </select>
                        
                        <button type="submit" class="search-button">Filter Results</button>
                    </div>
                </form>
            </div>

            <!-- Results Summary -->
            <?php if (is_search() || get_query_var('listing_city') || get_query_var('listing_rating') || get_query_var('directory_filter')) : ?>
                <div class="results-summary">
                    <h3>Search Results</h3>
                    <p>
                        <?php if (is_search()) : ?>
                            Showing results for: <strong>"<?php echo get_search_query(); ?>"</strong>
                        <?php endif; ?>
                        
                        <?php if (get_query_var('listing_city')) : ?>
                            in <strong><?php echo esc_html(get_query_var('listing_city')); ?></strong>
                        <?php endif; ?>
                        
                        <?php if (get_query_var('listing_rating')) : ?>
                            with <strong><?php echo esc_html(get_query_var('listing_rating')); ?>+ star rating</strong>
                        <?php endif; ?>
                        
                        <?php if (get_query_var('directory_filter')) : ?>
                            offering <strong><?php echo esc_html(get_term_by('slug', get_query_var('directory_filter'), 'directory')->name); ?></strong> services
                        <?php endif; ?>
                    </p>
                    
                    <a href="<?php echo esc_url(get_post_type_archive_link('listing')); ?>" class="clear-filters">
                        Clear all filters
                    </a>
                </div>
            <?php endif; ?>

            <div class="directory-grid">
                <?php
                while (have_posts()) :
                    the_post();
                    
                    // Get listing data (updated field names)
                    $listing_name = get_the_title(); // Use post title instead of listing_name
                    $listing_phone = get_post_meta(get_the_ID(), 'listing_phone', true);
                    $listing_address = get_post_meta(get_the_ID(), 'listing_full_address', true);
                    $listing_city = get_post_meta(get_the_ID(), 'listing_city', true);
                    $listing_rating = get_post_meta(get_the_ID(), 'listing_rating', true);
                    $listing_reviews = get_post_meta(get_the_ID(), 'listing_reviews', true);
                    $listing_photo = get_post_meta(get_the_ID(), 'listing_photo', true);
                    $listing_about = get_post_meta(get_the_ID(), 'listing_about', true);
                    $listing_verified = get_post_meta(get_the_ID(), 'listing_verified', true);
                    
                    // Get directory terms
                    $directories = get_the_terms(get_the_ID(), 'directory');
                ?>
                    <article class="listing-card">
                        <?php if ($listing_photo) : ?>
                            <div class="listing-image">
                                <img src="<?php echo esc_url($listing_photo); ?>" alt="<?php echo esc_attr($listing_name); ?>">
                                <?php if ($listing_verified) : ?>
                                    <span class="verified-badge">✓ Verified</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="listing-content">
                            <h3 class="listing-title">
                                <a href="<?php the_permalink(); ?>">
                                    <?php echo esc_html($listing_name); ?>
                                </a>
                            </h3>
                            
                            <?php if ($directories && !is_wp_error($directories)) : ?>
                                <div class="listing-meta">
                                    <?php
                                    $directory_names = array();
                                    foreach ($directories as $directory) {
                                        $directory_names[] = $directory->name;
                                    }
                                    echo esc_html(implode(', ', $directory_names));
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_about) : ?>
                                <div class="listing-excerpt">
                                    <?php echo wp_trim_words(wp_strip_all_tags($listing_about), 20, '...'); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_address) : ?>
                                <div class="listing-address">
                                    📍 <?php echo esc_html($listing_address); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_phone) : ?>
                                <div class="listing-phone">
                                    📞 <a href="tel:<?php echo esc_attr($listing_phone); ?>">
                                        <?php echo esc_html($listing_phone); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_rating) : ?>
                                <div class="listing-rating">
                                    <span class="stars"><?php echo get_rating_stars($listing_rating); ?></span>
                                    <span class="rating-text">
                                        <?php echo esc_html($listing_rating); ?>
                                        <?php if ($listing_reviews) : ?>
                                            (<?php echo esc_html($listing_reviews); ?> reviews)
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="listing-actions">
                                <a href="<?php the_permalink(); ?>" class="btn">View Details</a>
                                <?php if ($listing_phone) : ?>
                                    <a href="tel:<?php echo esc_attr($listing_phone); ?>" class="btn btn-secondary">Call Now</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size'  => 2,
                'prev_text' => __('Previous', 'odd'),
                'next_text' => __('Next', 'odd'),
            ));
            ?>

        <?php else : ?>

            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title">No dental practices found</h1>
                </header><!-- .page-header -->

                <div class="page-content">
                    <?php if (is_search()) : ?>
                        <p>Sorry, but nothing matched your search terms. Please try again with different keywords.</p>
                    <?php else : ?>
                        <p>No dental practices have been added to the directory yet.</p>
                    <?php endif; ?>

                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn">Return to Homepage</a>
                </div><!-- .page-content -->
            </section><!-- .no-results -->

        <?php endif; ?>
        
    </div><!-- .container -->
</main><!-- #main -->

<?php
get_footer();
